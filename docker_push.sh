#!/bin/bash
# 构建并推送Docker镜像到Docker Hub

# 配置
USERNAME="aliez0lie1"
IMAGE_NAME="jassistant"
VERSION="1.0.4"
FULL_IMAGE_NAME="$USERNAME/$IMAGE_NAME:$VERSION"

echo "===== 开始构建和推送Docker镜像 ====="
echo "镜像名称: $FULL_IMAGE_NAME"

# 确保已经登录Docker Hub
echo "正在检查Docker登录状态..."
if ! docker info | grep -q "Username"; then
  echo "您尚未登录Docker Hub，请先登录:"
  docker login
fi

# 构建Docker镜像
echo "正在构建Docker镜像..."
docker build -t $FULL_IMAGE_NAME .

# 构建最新版本标签
docker tag $FULL_IMAGE_NAME $USERNAME/$IMAGE_NAME:latest

# 推送到Docker Hub
echo "正在推送镜像到Docker Hub..."
docker push $FULL_IMAGE_NAME
docker push $USERNAME/$IMAGE_NAME:latest

echo "===== 完成! ====="
echo "镜像已成功推送到Docker Hub: $FULL_IMAGE_NAME"
echo "同时推送了latest标签: $USERNAME/$IMAGE_NAME:latest"
echo "您现在可以在Docker Hub上添加镜像描述和图标: https://hub.docker.com/r/$USERNAME/$IMAGE_NAME" 