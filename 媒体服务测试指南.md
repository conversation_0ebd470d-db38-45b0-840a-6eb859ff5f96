# 媒体服务模块测试指南

## 🎯 测试目标
验证媒体服务模块拆分后的功能完整性，确保：
- 媒体文件访问正常
- 水印文件访问正常  
- 封面缓存功能正常
- 路径安全检查有效
- API接口保持兼容

## 🚀 快速测试

### 1. 自动化测试
```bash
# 运行测试脚本
python test_media_api.py
```

### 2. 启动应用测试
```bash
# 进入backend目录
cd backend

# 启动应用
python app.py
```

如果启动成功且无错误，说明模块拆分正确。

## 🔍 详细测试步骤

### 测试1: 模块导入测试
```python
# 在Python控制台中执行
import sys
sys.path.append('backend')

from api.media_api import media_api, get_media_root, is_safe_path
print("✅ 导入成功")

# 测试函数
print(f"媒体根路径: {get_media_root()}")
print(f"路径安全检查: {is_safe_path('/weiam/test.mp4')}")
```

### 测试2: API路由测试
启动应用后，使用curl或浏览器测试：

```bash
# 1. 测试设置API（确认media_root配置）
curl http://localhost:34711/api/settings

# 2. 测试媒体文件访问（应返回404，因为文件不存在）
curl -I http://localhost:34711/api/media/nonexistent.jpg

# 3. 测试水印文件访问
curl -I http://localhost:34711/api/watermarks/4k.png

# 4. 测试封面缓存访问
curl -I http://localhost:34711/api/media/cover_cache/test.jpg
```

### 测试3: 前端功能测试
1. **文件管理器测试**
   - 访问 http://localhost:34711
   - 进入文件管理器页面
   - 检查是否能正常浏览媒体目录
   - 验证路径显示是否正确

2. **图片显示测试**
   - 在主页查看电影封面
   - 检查封面图片是否正常加载
   - 测试缩略图显示

3. **水印功能测试**
   - 进入图片处理页面
   - 上传图片并添加水印
   - 检查水印是否正常显示

### 测试4: 安全性测试
测试路径遍历攻击防护：

```bash
# 这些请求应该被拒绝（返回403）
curl -I "http://localhost:34711/api/media/../../../etc/passwd"
curl -I "http://localhost:34711/api/media/..%2F..%2F..%2Fetc%2Fpasswd"
curl -I "http://localhost:34711/api/watermarks/../../../etc/passwd"
```

## 📋 测试检查清单

### ✅ 基础功能
- [ ] 应用启动无错误
- [ ] 模块导入成功
- [ ] API路由注册正确
- [ ] 媒体根路径获取正常

### ✅ 媒体文件服务
- [ ] `/api/media/<filename>` 路由响应正常
- [ ] 存在的文件能正确返回
- [ ] 不存在的文件返回404
- [ ] 封面缓存路径处理正确

### ✅ 水印文件服务  
- [ ] `/api/watermarks/<filename>` 路由响应正常
- [ ] 水印文件能正确返回
- [ ] 不存在的水印文件返回404

### ✅ 安全性检查
- [ ] 路径遍历攻击被阻止
- [ ] 非法路径返回403
- [ ] 安全路径检查函数工作正常

### ✅ 前端兼容性
- [ ] 文件管理器正常工作
- [ ] 图片显示正常
- [ ] 水印功能正常
- [ ] 设置页面正常

## 🐛 常见问题排查

### 问题1: 导入错误
```
ModuleNotFoundError: No module named 'api.media_api'
```
**解决方案**: 检查文件路径和Python路径设置

### 问题2: 404错误
```
404 Not Found - /api/media/test.jpg
```
**检查项**:
- 文件是否存在于正确路径
- 媒体根路径配置是否正确
- 路径权限是否正确

### 问题3: 403禁止访问
```
403 Forbidden
```
**检查项**:
- 路径是否在允许范围内
- 是否触发了安全检查
- 媒体根路径设置是否正确

### 问题4: 应用启动失败
**检查项**:
- 依赖模块是否正确导入
- 配置文件是否存在
- 端口是否被占用

## 📊 性能测试

### 并发访问测试
```bash
# 使用ab工具测试并发访问
ab -n 100 -c 10 http://localhost:34711/api/media/test.jpg
```

### 大文件访问测试
```bash
# 测试大文件下载
curl -o /dev/null http://localhost:34711/api/media/large_video.mp4
```

## 🎉 测试完成标准

当以下条件全部满足时，认为媒体服务模块拆分测试通过：

1. ✅ 自动化测试脚本全部通过
2. ✅ 应用启动无错误日志
3. ✅ 所有API路由响应正常
4. ✅ 前端功能无异常
5. ✅ 安全检查有效
6. ✅ 性能无明显下降

## 📝 测试报告模板

```
媒体服务模块拆分测试报告
========================

测试时间: [填写时间]
测试环境: [填写环境信息]
测试人员: [填写姓名]

测试结果:
- 基础功能: ✅/❌
- 媒体文件服务: ✅/❌  
- 水印文件服务: ✅/❌
- 安全性检查: ✅/❌
- 前端兼容性: ✅/❌

发现问题:
1. [问题描述]
2. [问题描述]

总结:
[测试总结]
```
