# backend/api_handler.py
"""
核心API处理模块
负责处理NFO管理、图片处理、元数据获取等核心业务逻辑
这些功能由于业务关系复杂、高度耦合，保持在同一模块中
"""
import os
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging
import xml.etree.ElementTree as ET
import urllib.parse
import re
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

from flask import Blueprint, request, jsonify, current_app
from bs4 import BeautifulSoup

from db_manager import get_db_connection
import image_processor
from config_utils import get_settings
from nfo_parser import parse_nfo_file
from utils import (is_safe_path as utils_is_safe_path, get_safe_filename,
                  ensure_dir_exists, HTTP_HEADERS, safe_rename, safe_copy, safe_delete)

# 导入API模块
from api.settings_api import settings_api
from api.performance_api import performance_api
from api.media_api import media_api, get_media_root, is_safe_path
from api.file_api import file_api
from api.log_api import log_api
from api.content_api import content_api
from api.image_api import image_api

# ==================== 全局配置和工具函数 ====================

def create_optimized_session():
    """创建优化的requests Session，支持连接池和重试"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=2,  # 总重试次数
        backoff_factor=0.5,  # 重试间隔
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
    )

    # 配置HTTP适配器，支持连接池
    adapter = HTTPAdapter(
        pool_connections=10,  # 连接池大小
        pool_maxsize=20,      # 每个连接池的最大连接数
        max_retries=retry_strategy,
        pool_block=False      # 非阻塞模式
    )

    # 为HTTP和HTTPS配置适配器
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 启用Keep-Alive（默认已启用，但明确设置）
    session.headers.update({
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100'
    })

    return session

# 创建全局Session实例，用于链接验证
_http_session = create_optimized_session()

# DMM域名缓存
_dmm_domain_cache = {
    'status': None,  # 'available', 'unavailable', None
    'last_check': None,
    'cache_duration': 300  # 5分钟缓存
}

# 创建API蓝图
api = Blueprint('api', __name__)

def check_dmm_domain_availability():
    """检查DMM域名可用性 - 跳过检测，直接返回可用"""
    # 用户确认网站可以访问，跳过域名检测以避免不必要的延迟
    return True

def is_dmm_url(url):
    """判断是否为DMM链接"""
    return url and 'awsimgsrc.dmm.co.jp' in url

class ScrapeError(Exception):
    """用于抓取过程中的错误处理"""
    pass

# ==================== 元数据获取相关功能 ====================

def scrape_cid(bangou: str) -> str:
    """
    从 avbase.net 搜索并解析出 CID
    """
    search_url = f"https://www.avbase.net/works?q={urllib.parse.quote(bangou)}"
    current_app.logger.info(f"正在访问: {search_url}")
    try:
        response = requests.get(search_url, headers=HTTP_HEADERS, timeout=20)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'lxml')
        
        fanza_img = soup.find('img', alt='fanza')
        
        if not fanza_img:
            raise ScrapeError(f"在AVBase页面中未找到 'fanza' 图标 (可能无此番号记录或页面结构已更改)")
        
        fanza_anchor = fanza_img.find_parent('a')
        if not fanza_anchor or not fanza_anchor.has_attr('href'):
            raise ScrapeError("找到了'fanza'图标，但未能找到其包含链接的父标签")
        
        dmm_url_encoded = fanza_anchor['href']
        dmm_url_decoded = urllib.parse.unquote(dmm_url_encoded)
        
        match = re.search(r'cid=([a-zA-Z0-9_]+)', dmm_url_decoded)
        if not match:
            raise ScrapeError(f"在解码后的链接中未能解析出CID: {dmm_url_decoded}")
        
        found_cid = match.group(1)
        current_app.logger.info(f"成功找到CID: {found_cid}")
        return found_cid
        
    except requests.exceptions.RequestException as e:
        raise ScrapeError(f"网络请求失败: {e}")

# 添加新的API端点用于手动获取CID信息
@api.route('/get-manual-cid-info', methods=['GET'])
def get_manual_cid_info():
    bangou = request.args.get('bangou')
    if not bangou: return jsonify({"success": False, "message": "需要提供番号"}), 400
    
    try:
        # 使用scrape_cid获取CID
        cid = scrape_cid(bangou)
        
        if not cid:
            return jsonify({"success": False, "message": "未找到CID"}), 404
        
        # 与 get_dmm_info 一样构造结果
        parts = cid.split('00')
        code = parts[0] + parts[-1].zfill(5) if len(parts) > 1 else cid
        
        result = {
            "cid": cid,
            "rule_info": {"manual": True},
            "wallpaper_url": {"url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg"},
            "cover_url": {"url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg"}
        }
        
        return jsonify({"success": True, "results": [result]})
        
    except ScrapeError as e:
        current_app.logger.error(f"手动获取CID失败: {e}")
        return jsonify({"success": False, "message": f"获取CID失败: {e}"}), 404
    except Exception as e:
        current_app.logger.error(f"手动获取CID时发生错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"处理请求失败: {e}"}), 500

@api.route('/get-dmm-info', methods=['GET'])
def get_dmm_info():
    bangou = request.args.get('bangou')
    if not bangou: return jsonify({"success": False, "message": "需要提供番号"}), 400
    api_url = current_app.config['CID_API_URL']
    api_key = current_app.config['CID_API_KEY']
    try:
        response = requests.get(api_url, params={'bangou': bangou}, headers={'X-API-KEY': api_key}, timeout=15)
        response.raise_for_status()
        cid_data = response.json()
        if not cid_data.get("success") or not cid_data.get("results"): return jsonify({"success": False, "message": "未找到CID"}), 404
    except requests.RequestException as e: return jsonify({"success": False, "message": f"查询CID失败: {e}"}), 500
    results = []
    for res in cid_data.get("results", []):
        cid = res.get("cid")
        if not cid: continue
        parts = cid.split('00')
        code = parts[0] + parts[-1].zfill(5) if len(parts) > 1 else cid

        results.append({"cid": cid, "rule_info": res.get("rule_info"), "wallpaper_url": {"url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg"}, "cover_url": {"url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg"},})
    return jsonify({"success": True, "results": results})

def get_cached_verification(url):
    """从缓存中获取链接验证结果"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询缓存（永久有效，除非强制刷新）
        cursor.execute("""
            SELECT status_code, is_valid, cid
            FROM link_verification_cache
            WHERE url = ?
        """, (url,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                "url": url,
                "status_code": result[0],
                "valid": bool(result[1]),
                "cid": result[2]
            }
        return None
    except Exception as e:
        current_app.logger.error(f"获取缓存失败: {e}")
        return None

def cache_verification_result(url, status_code, is_valid, cid=None):
    """缓存链接验证结果（永久有效）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT OR REPLACE INTO link_verification_cache
            (url, cid, status_code, is_valid, verified_at)
            VALUES (?, ?, ?, ?, datetime('now'))
        """, (url, cid, status_code, is_valid))

        conn.commit()
        conn.close()

    except Exception as e:
        current_app.logger.error(f"缓存验证结果失败: {e}")

# ==================== 链接验证相关功能 ====================

@api.route('/clear-link-cache', methods=['POST'])
def clear_link_cache():
    """清除链接验证缓存"""
    try:
        data = request.get_json()
        if data and 'url' in data:
            # 清除特定URL的缓存
            url = data['url']
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("DELETE FROM link_verification_cache WHERE url = ?", (url,))
            conn.commit()
            conn.close()
            return jsonify({"success": True, "message": f"已清除 {url} 的缓存"})
        else:
            # 清除所有缓存
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("DELETE FROM link_verification_cache")
            conn.commit()
            conn.close()

            # 同时清除DMM域名缓存
            global _dmm_domain_cache
            _dmm_domain_cache['status'] = None
            _dmm_domain_cache['last_check'] = None

            return jsonify({"success": True, "message": "已清除所有链接验证缓存和DMM域名缓存"})
    except Exception as e:
        current_app.logger.error(f"清除缓存失败: {e}")
        return jsonify({"success": False, "message": f"清除缓存失败: {e}"}), 500

@api.route('/clear-dmm-domain-cache', methods=['POST'])
def clear_dmm_domain_cache():
    """清除DMM域名缓存"""
    try:
        global _dmm_domain_cache
        _dmm_domain_cache['status'] = None
        _dmm_domain_cache['last_check'] = None

        return jsonify({
            "success": True,
            "message": "已清除DMM域名缓存"
        })
    except Exception as e:
        current_app.logger.error(f"清除DMM域名缓存失败: {e}")
        return jsonify({"success": False, "message": f"清除DMM域名缓存失败: {e}"}), 500

@api.route('/verify-links', methods=['POST'])
def verify_links():
    """
    批量验证链接有效性
    接收链接数组，返回每个链接的验证状态
    支持强制刷新缓存
    """
    try:
        data = request.get_json()
        if not data or 'links' not in data:
            return jsonify({"success": False, "message": "需要提供links数组"}), 400

        links = data['links']
        force_refresh = data.get('force_refresh', False)  # 是否强制刷新缓存
        cid = data.get('cid')  # 可选的CID参数

        if not isinstance(links, list):
            return jsonify({"success": False, "message": "links必须是数组"}), 400

        def verify_single_link(url):
            """验证单个链接的有效性，支持HTTP缓存协商和DMM域名缓存"""

            # DMM域名优化：如果是DMM链接且域名不可用，直接返回失败
            if is_dmm_url(url) and not check_dmm_domain_availability():
                current_app.logger.debug(f"🚫 DMM域名不可用，跳过验证: {url}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": "DMM域名不可用"
                }

            # 检查缓存，获取之前的缓存协商头
            cached_result = get_cached_verification(url)

            if not force_refresh and cached_result:
                # 如果不是强制刷新且有缓存，直接返回（永久有效）
                return cached_result

            try:
                try:
                    # 使用4秒超时，适合DMM服务器
                    timeout = 4

                    # 使用更完整的浏览器请求头
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Referer': 'https://www.dmm.co.jp/'
                    }

                    # 使用GET请求，stream=False提高稳定性
                    response = _http_session.get(url, timeout=timeout, headers=headers, allow_redirects=True, stream=False)
                    status_code = response.status_code

                except requests.exceptions.Timeout as timeout_e:
                    current_app.logger.error(f"⏰ 请求超时: {timeout_e}")
                    raise timeout_e
                except requests.exceptions.ConnectionError as conn_e:
                    current_app.logger.error(f"🌐 连接错误: {conn_e}")
                    raise conn_e
                except requests.exceptions.RequestException as req_e:
                    current_app.logger.error(f"� 请求异常: {req_e}")
                    raise req_e
                except Exception as general_e:
                    current_app.logger.error(f"💥 未知异常: {general_e}")
                    import traceback
                    current_app.logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
                    raise general_e

                # 判断链接是否有效
                is_valid = 200 <= status_code < 400

                result = {
                    "url": url,
                    "status_code": status_code,
                    "valid": is_valid
                }

                # 缓存验证结果（永久有效）
                cache_verification_result(url, status_code, is_valid, cid)

                return result
            except requests.exceptions.Timeout as e:
                current_app.logger.warning(f"⏰ 请求超时 (4秒): {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 408,
                    "valid": False,
                    "error": f"请求超时 (4秒): {str(e)}"
                }
            except requests.exceptions.SSLError as e:
                current_app.logger.error(f"🔒 SSL错误: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"SSL错误: {str(e)}"
                }
            except requests.exceptions.ConnectionError as e:
                current_app.logger.error(f"🌐 连接错误: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"连接错误: {str(e)}"
                }
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"🚫 请求异常: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"请求异常: {str(e)}"
                }
            except Exception as e:
                current_app.logger.error(f"💥 未知异常: {url} - {str(e)}")
                import traceback
                current_app.logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"验证失败: {str(e)}"
                }

        # 并行验证所有链接以提高速度
        import concurrent.futures

        results = []
        valid_links = []

        # 预处理链接，分离DMM和非DMM链接
        dmm_links = []
        other_links = []

        for link in links:
            url = None
            if isinstance(link, str):
                url = link
            elif isinstance(link, dict) and 'url' in link:
                url = link['url']
            else:
                results.append({
                    "url": str(link),
                    "status_code": 0,
                    "valid": False,
                    "error": "无效的链接格式"
                })
                continue

            if is_dmm_url(url):
                dmm_links.append(url)
            else:
                other_links.append(url)

        valid_links = dmm_links + other_links

        # DMM域名批量优化：如果DMM域名不可用，批量标记所有DMM链接为失败
        if dmm_links and not check_dmm_domain_availability():
            current_app.logger.warning(f"🚫 DMM域名不可用，批量跳过{len(dmm_links)}个DMM链接")
            for url in dmm_links:
                results.append({
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": "DMM域名不可用"
                })
            # 只验证非DMM链接
            valid_links = other_links

        # 并行验证链接
        if valid_links:
            # 在主线程中获取应用实例
            app = current_app._get_current_object()

            # 创建一个包装函数，在Flask应用上下文中执行验证
            def verify_with_context(url):
                try:
                    with app.app_context():
                        return verify_single_link(url)
                except Exception as e:
                    # 使用app.logger而不是current_app.logger
                    with app.app_context():
                        app.logger.error(f"验证链接异常: {url} - {str(e)}")
                    return {
                        "url": url,
                        "status_code": 0,
                        "valid": False,
                        "error": f"验证异常: {str(e)}"
                    }

            # 使用线程池并行验证，最大4个并发
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                # 提交所有验证任务
                future_to_url = {executor.submit(verify_with_context, url): url for url in valid_links}

                # 收集结果，保持原始顺序
                url_results = {}
                for future in concurrent.futures.as_completed(future_to_url):
                    url = future_to_url[future]
                    try:
                        result = future.result()
                        url_results[url] = result
                    except Exception as e:
                        # 使用app.logger而不是current_app.logger
                        with app.app_context():
                            app.logger.error(f"并行验证异常: {url} - {str(e)}")
                        url_results[url] = {
                            "url": url,
                            "status_code": 0,
                            "valid": False,
                            "error": f"并行验证异常: {str(e)}"
                        }

                # 按原始顺序添加结果
                for url in valid_links:
                    results.append(url_results[url])

        return jsonify({"success": True, "results": results})

    except Exception as e:
        current_app.logger.error(f"验证链接时发生错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"验证链接失败: {e}"}), 500



# ==================== NFO管理相关功能 ====================

@api.route('/manual/find-movie', methods=['GET'])
def find_movie_by_query():
    query = request.args.get('q', '').strip()
    if not query: return jsonify([])
    conn = get_db_connection()
    search_query = f"%{query}%"
    movies = conn.execute("SELECT id, bangou, title, item_path FROM movies WHERE bangou LIKE ? OR item_path LIKE ? LIMIT 10", (search_query, search_query)).fetchall()
    conn.close()
    return jsonify([dict(row) for row in movies])

@api.route('/manual/movie-details/<int:movie_id>', methods=['GET'])
def get_movie_details(movie_id):
    conn = get_db_connection()
    movie = conn.execute("SELECT * FROM movies WHERE id = ?", (movie_id,)).fetchone()
    if not movie: conn.close(); return jsonify({"error": "未找到电影"}), 404
    pictures = conn.execute("SELECT * FROM pictures WHERE movie_id = ?", (movie_id,)).fetchone()
    nfo_records = conn.execute("SELECT id, nfo_path FROM nfo_data WHERE movie_id = ?", (movie_id,)).fetchall()
    conn.close()
    return jsonify({"movie": dict(movie), "pictures": dict(pictures) if pictures else {}, "nfo_files": [dict(row) for row in nfo_records]})

# 修改get_nfo_content函数
@api.route('/manual/nfo-content/<int:nfo_id>', methods=['GET'])
def get_nfo_content(nfo_id):
    """获取数据清洗模式的NFO内容"""
    conn = get_db_connection()
    nfo_record = conn.execute("SELECT nfo_path FROM nfo_data WHERE id = ?", (nfo_id,)).fetchone()
    conn.close()
    
    if not nfo_record: 
        return jsonify({"error": "未找到NFO记录"}), 404
        
    nfo_path = nfo_record['nfo_path']
    if not is_safe_path(nfo_path): 
        return jsonify({"error": "无效的NFO路径"}), 400
        
    try:
        # 解析NFO文件
        nfo_data = parse_nfo_file(nfo_path)
        
        # 确保返回的是可序列化的数据
        if nfo_data and '_nfo_path' in nfo_data:
            nfo_data.pop('_nfo_path', None)
            
        if not nfo_data:
            return jsonify({"error": "NFO文件解析失败"}), 500
            
        return jsonify(nfo_data)
    except Exception as e: 
        current_app.logger.error(f"读取NFO文件失败: {e}", exc_info=True)
        return jsonify({"error": f"读取NFO文件失败: {e}"}), 500

# 修改save_nfo_content函数
@api.route('/manual/save-nfo/<int:nfo_id>', methods=['POST'])
def save_nfo_content(nfo_id):
    """数据清洗模式保存NFO文件，同时更新数据库"""
    data = request.json
    if not data:
        return jsonify({"success": False, "message": "请求数据为空"}), 400
        
    conn = get_db_connection()
    
    try:
        # 获取NFO记录
        nfo_record = conn.execute("SELECT nfo_path, strm_name FROM nfo_data WHERE id = ?", (nfo_id,)).fetchone()
        if not nfo_record:
            conn.close()
            return jsonify({"success": False, "message": "未找到NFO记录"}), 404
            
        nfo_path = nfo_record['nfo_path']
        # 修复: sqlite3.Row对象使用索引方式访问，不要用.get()方法
        # 如果strm_name不存在，使用空字符串作为默认值
        strm_name = nfo_record['strm_name'] if 'strm_name' in nfo_record.keys() else ''
        
        if not is_safe_path(nfo_path):
            conn.close()
            return jsonify({"success": False, "message": "无效的NFO路径"}), 400
            
        # 处理标题和原始标题，从数据库角度需要拼接番号，但在NFO中已由save_nfo_file处理
        from nfo_parser import extract_bangou_from_title, save_nfo_file
        
        # 保存到NFO文件，使用'database'模式，确保适当处理番号
        success, message = save_nfo_file(nfo_path, data, mode='database')
        if not success:
            conn.close()
            return jsonify({"success": False, "message": message}), 500
            
        # 处理数据库更新
        # 为数据库中的字段处理：提取标题中的番号并清理
        _, clean_title = extract_bangou_from_title(data.get('title', ''))
        if 'title' in data:
            data['title'] = clean_title
            
        # 同样处理originaltitle
        if 'originaltitle' in data:
            _, clean_orig_title = extract_bangou_from_title(data.get('originaltitle', ''))
            data['originaltitle'] = clean_orig_title
        
        # 更新数据库中的NFO记录
        nfo_main_cols = ['originaltitle', 'plot', 'originalplot', 'tagline', 'release_date', 'year', 'rating', 'criticrating']
        nfo_main_vals = [data.get(col) for col in nfo_main_cols]
        
        # 仅更新存在的字段
        update_cols = []
        update_vals = []
        
        for i, col in enumerate(nfo_main_cols):
            if col in data:
                update_cols.append(f"{col}=?")
                update_vals.append(nfo_main_vals[i])
                
        if update_cols:
            conn.execute(f"UPDATE nfo_data SET {', '.join(update_cols)} WHERE id = ?", (*update_vals, nfo_id))
        
        # 处理相关映射（演员、类型等）
        from webhook_handler import handle_nfo_mappings
        handle_nfo_mappings(conn.cursor(), nfo_id, data)
        
        conn.commit()
        return jsonify({"success": True, "message": "NFO 已成功保存并更新数据库"})
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"保存NFO失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"保存失败: {e}"}), 500
    finally:
        conn.close()

# 修改get_handmade_nfo_details函数
@api.route('/handmade/nfo-details', methods=['GET'])
def get_handmade_nfo_details():
    """获取手作修正模式的NFO详情"""
    nfo_path = request.args.get('path')
    if not nfo_path or not is_safe_path(nfo_path): 
        return jsonify({"error": "无效的NFO路径"}), 400
        
    base_path = os.path.splitext(nfo_path)[0]
    poster_info = image_processor.get_image_details(f"{base_path}-poster.jpg")
    fanart_info = image_processor.get_image_details(f"{base_path}-fanart.jpg")
    thumb_info = image_processor.get_image_details(f"{base_path}-thumb.jpg")
    pictures = {
        "poster_path": f"{base_path}-poster.jpg", "poster_stats": poster_info,
        "fanart_path": f"{base_path}-fanart.jpg", "fanart_stats": fanart_info,
        "thumb_path": f"{base_path}-thumb.jpg", "thumb_stats": thumb_info,
    }
    
    # 使用修改后的NFO解析器获取数据
    nfo_data = parse_nfo_file(nfo_path)
    
    # 确保返回的是可序列化的数据
    if nfo_data and '_nfo_path' in nfo_data:
        # 不需要在JSON中传递内部字段
        nfo_data.pop('_nfo_path', None)
        
    return jsonify({"pictures": pictures, "nfo_data": nfo_data})

# 修改save_handmade_nfo函数
@api.route('/handmade/save-nfo', methods=['POST'])
def save_handmade_nfo():
    """手作修正模式保存NFO文件"""
    nfo_path = request.args.get('path')
    if not nfo_path or not is_safe_path(nfo_path):
        return jsonify({"success": False, "message": "无效的NFO路径"}), 400
    
    try:
        data = request.json
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
            
        from nfo_parser import save_nfo_file
        
        # 使用'handmade'模式，仅修改NFO文件，不更新数据库
        success, message = save_nfo_file(nfo_path, data, mode='handmade')
        
        if success:
            return jsonify({"success": True, "message": "NFO文件保存成功"})
        else:
            return jsonify({"success": False, "message": message}), 500
    except Exception as e:
        current_app.logger.error(f"保存NFO文件失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"保存NFO文件失败: {e}"}), 500



# ==================== 应用初始化 ====================

def init_app(app):
    """注册所有API蓝图到Flask应用"""
    # 注册核心API（NFO、图片、元数据等复杂业务逻辑）
    app.register_blueprint(api, url_prefix='/api')

    # 注册独立功能模块API
    app.register_blueprint(settings_api, url_prefix='/api')      # 设置管理
    app.register_blueprint(performance_api, url_prefix='/api')   # 性能监控
    app.register_blueprint(media_api, url_prefix='/api')         # 媒体文件服务
    app.register_blueprint(file_api, url_prefix='/api')          # 文件操作
    app.register_blueprint(log_api, url_prefix='/api')           # 日志管理
    app.register_blueprint(content_api, url_prefix='/api')       # 内容数据查询
    app.register_blueprint(image_api, url_prefix='/api')         # 图片处理
