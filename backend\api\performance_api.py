# backend/api/performance_api.py
"""
性能监控模块
处理数据库性能分析、缓存管理、系统监控等功能
"""
import time
from flask import Blueprint, request, jsonify, current_app

# 导入性能优化模块
from db_performance import db_performance_optimizer
from cache_manager import cache_manager
from monitoring import monitoring_system
from performance_test import performance_tester
from db_utils import db_manager

# 创建性能监控API蓝图
performance_api = Blueprint('performance_api', __name__)

@performance_api.route('/performance/database/analyze', methods=['GET'])
def analyze_database_performance():
    """分析数据库性能"""
    try:
        analysis = db_performance_optimizer.analyze_database_performance()
        return jsonify({
            "success": True,
            "data": analysis
        })
    except Exception as e:
        current_app.logger.error(f"数据库性能分析失败: {e}")
        return jsonify({
            "success": False,
            "message": f"分析失败: {str(e)}"
        }), 500

@performance_api.route('/performance/database/optimize', methods=['POST'])
def optimize_database():
    """优化数据库"""
    try:
        # 创建缺失的索引
        index_result = db_performance_optimizer.create_missing_indexes()

        # 执行VACUUM
        vacuum_result = db_performance_optimizer.vacuum_database()

        return jsonify({
            "success": True,
            "data": {
                "indexes": index_result,
                "vacuum": vacuum_result
            }
        })
    except Exception as e:
        current_app.logger.error(f"数据库优化失败: {e}")
        return jsonify({
            "success": False,
            "message": f"优化失败: {str(e)}"
        }), 500

@performance_api.route('/performance/cache/stats', methods=['GET'])
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_manager.get_comprehensive_stats()
        return jsonify({
            "success": True,
            "data": stats
        })
    except Exception as e:
        current_app.logger.error(f"获取缓存统计失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取统计失败: {str(e)}"
        }), 500

@performance_api.route('/performance/cache/clear', methods=['POST'])
def clear_cache():
    """清空缓存"""
    try:
        cache_type = request.json.get('type', 'all') if request.json else 'all'

        if cache_type == 'all':
            result = cache_manager.clear_all_caches()
        elif cache_type == 'memory':
            cache_manager.memory_cache.clear()
            result = {'memory_cache_cleared': True}
        elif cache_type == 'file':
            result = {'file_cache_deleted': cache_manager.file_cache.clear()}
        elif cache_type == 'expired':
            result = cache_manager.cleanup_all_expired()
        else:
            return jsonify({
                "success": False,
                "message": "无效的缓存类型"
            }), 400

        return jsonify({
            "success": True,
            "data": result
        })
    except Exception as e:
        current_app.logger.error(f"清空缓存失败: {e}")
        return jsonify({
            "success": False,
            "message": f"清空失败: {str(e)}"
        }), 500

@performance_api.route('/performance/monitoring/dashboard', methods=['GET'])
def get_monitoring_dashboard():
    """获取监控面板数据"""
    try:
        dashboard_data = monitoring_system.get_dashboard_data()
        return jsonify({
            "success": True,
            "data": dashboard_data
        })
    except Exception as e:
        current_app.logger.error(f"获取监控数据失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}"
        }), 500

@performance_api.route('/performance/test/comprehensive', methods=['POST'])
def run_performance_test():
    """运行综合性能测试"""
    try:
        test_results = performance_tester.run_comprehensive_test()
        return jsonify({
            "success": True,
            "data": test_results
        })
    except Exception as e:
        current_app.logger.error(f"性能测试失败: {e}")
        return jsonify({
            "success": False,
            "message": f"测试失败: {str(e)}"
        }), 500

@performance_api.route('/performance/system/status', methods=['GET'])
def get_system_status():
    """获取系统状态概览"""
    try:
        # 获取数据库统计
        db_status = db_manager.get_database_status()

        # 获取缓存统计
        cache_stats = cache_manager.get_comprehensive_stats()

        # 获取监控数据
        monitoring_data = monitoring_system.get_dashboard_data()

        return jsonify({
            "success": True,
            "data": {
                "database": db_status,
                "cache": cache_stats,
                "monitoring": monitoring_data,
                "timestamp": time.time()
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}"
        }), 500
