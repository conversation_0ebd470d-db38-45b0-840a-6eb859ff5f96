# API Handler 代码结构整理

## 整理后的 `api_handler.py` 结构

### 1. 文件头部和导入 (1-38行)
```python
# 模块说明文档
# 标准库导入
# 第三方库导入  
# 项目内部导入
# API模块导入
```

### 2. 全局配置和工具函数 (40-84行)
```python
# ==================== 全局配置和工具函数 ====================
- create_optimized_session()     # HTTP会话配置
- _http_session                  # 全局会话实例
- _dmm_domain_cache             # DMM域名缓存
- api = Blueprint()             # API蓝图创建
- check_dmm_domain_availability() # DMM域名检查
- is_dmm_url()                  # DMM链接判断
- ScrapeError                   # 异常类定义
```

### 3. 元数据获取相关功能 (98-236行)
```python
# ==================== 元数据获取相关功能 ====================
- scrape_cid()                  # 从AVBase抓取CID
- @api.route('/get-manual-cid-info')    # 手动获取CID信息
- @api.route('/get-dmm-info')           # 获取DMM信息
- get_cached_verification_result()      # 获取缓存的验证结果
- cache_verification_result()           # 缓存验证结果
```

### 4. 链接验证相关功能 (238-514行)
```python
# ==================== 链接验证相关功能 ====================
- @api.route('/clear-link-cache')       # 清除链接缓存
- @api.route('/clear-dmm-domain-cache') # 清除DMM域名缓存
- @api.route('/verify-links')           # 批量验证链接有效性
- verify_single_link()                  # 单个链接验证
```

### 5. 图片处理相关功能 (520-593行)
```python
# ==================== 图片处理相关功能 ====================
- _update_db_pic_info()                 # 更新数据库图片信息
- @api.route('/process/poster')         # 处理海报图片
- @api.route('/process/fanart-and-thumb') # 处理背景和缩略图
- @api.route('/process/upload-image')   # 处理上传图片
```

### 6. NFO管理相关功能 (595-856行)
```python
# ==================== NFO管理相关功能 ====================
- @api.route('/manual/find-movie')      # 查找电影
- @api.route('/manual/movie-details/<int:movie_id>') # 获取电影详情
- @api.route('/manual/nfo-content/<int:nfo_id>')     # 获取NFO内容
- @api.route('/manual/save-nfo/<int:nfo_id>')        # 保存NFO内容
- @api.route('/handmade/nfo-details')   # 获取手作NFO详情
- @api.route('/handmade/save-nfo')      # 保存手作NFO
```

### 7. 应用初始化 (857-870行)
```python
# ==================== 应用初始化 ====================
- init_app()                    # 注册所有API蓝图
```

## 整理的改进点

### 1. 清晰的功能分组
- 使用明确的分组注释 `# ==================== 功能模块 ====================`
- 按业务逻辑相关性组织代码
- 每个分组内的函数按调用关系排序

### 2. 改进的文档说明
- 添加了模块级别的文档说明
- 解释了为什么这些功能保持在同一文件中
- 明确了各个功能模块的职责

### 3. 逻辑清晰的代码顺序
```
导入和配置 → 工具函数 → 元数据获取 → 链接验证 → 图片处理 → NFO管理 → 应用初始化
```

### 4. 保持业务完整性
- NFO、图片、元数据功能高度耦合，保持在同一模块
- 共享数据库事务和错误处理
- 统一的业务流程管理

## 与其他API模块的关系

### 已拆分的独立模块
- `content_api.py` - 内容数据查询（最新项目、低画质项目等）
- `file_api.py` - 文件操作管理
- `log_api.py` - 日志管理
- `media_api.py` - 媒体文件服务
- `settings_api.py` - 设置管理
- `performance_api.py` - 性能监控

### 核心业务模块（保持在api_handler.py）
- 元数据获取和处理
- 图片处理和水印添加
- NFO文件管理和解析
- 链接验证和缓存管理

这种结构既保持了核心业务逻辑的完整性，又实现了功能模块的合理分离。
