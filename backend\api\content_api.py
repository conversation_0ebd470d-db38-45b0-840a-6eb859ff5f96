# backend/api/content_api.py
"""
内容数据API模块
负责处理电影内容的查询、统计等功能
"""
import os
import logging
from flask import Blueprint, request, jsonify, current_app
from db_manager import get_db_connection
from config_utils import get_settings
from api.file_api import copy_to_cover_cache, get_cached_cover_path, manage_cover_cache

logger = logging.getLogger(__name__)

# 创建内容API蓝图
content_api = Blueprint('content_api', __name__)

def get_latest_high_quality_items(count):
    """获取最新的高画质项目
    
    Args:
        count: 要获取的项目数量
        
    Returns:
        list: 包含项目信息的字典列表
    """
    conn = get_db_connection()
    try:
        # 查询，从nfo_data表中获取strm_name
        query = """
            SELECT m.id, m.item_path, m.bangou, m.title, p.poster_path, p.poster_status,
                   COALESCE(n.strm_name, m.bangou) as strm_name
            FROM movies m
            LEFT JOIN pictures p ON m.id = p.movie_id
            LEFT JOIN nfo_data n ON m.id = n.movie_id
            WHERE p.poster_status = '高画质'
            ORDER BY m.created_at DESC, m.id DESC
            LIMIT ?
        """
        items = conn.execute(query, (count,)).fetchall()
        
        # 转换为列表字典
        return [dict(row) for row in items]
    finally:
        conn.close()

@content_api.route('/latest-items')
def get_latest_items():
    """获取最新入库的高画质项目"""
    try:
        settings = get_settings()
        count = settings.get('latest_movies_count', 24)
        
        # 获取最新的高画质项目
        items_list = get_latest_high_quality_items(count)
        
        # 处理封面缓存
        use_cache = settings.get('use_cover_cache', True)  # 默认启用缓存
        if use_cache:
            for item in items_list:
                poster_path = item.get('poster_path')
                strm_name = item.get('strm_name')
                
                if poster_path and strm_name:
                    # 尝试获取缓存的封面
                    cached_path = get_cached_cover_path(strm_name)
                    if not cached_path:
                        # 如果缓存不存在，创建缓存
                        cached_path = copy_to_cover_cache(poster_path, strm_name)
                    
                    if cached_path:
                        # 确保路径格式为 'cover_cache/文件名.jpg'
                        # 注意：这里不需要再次调用secure_filename，因为copy_to_cover_cache和get_cached_cover_path已经处理过了
                        item['poster_path'] = os.path.join('cover_cache', os.path.basename(cached_path))
                        
                        # 记录调试信息
                        current_app.logger.debug(f"使用缓存封面: {item['poster_path']} (原路径: {poster_path})")
            
            # 管理缓存，删除多余的
            manage_cover_cache()
        
        return jsonify(items_list)
    except Exception as e:
        current_app.logger.error(f"获取最新项目失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取最新项目失败: {str(e)}"}), 500

@content_api.route('/low-quality-items', methods=['GET'])
def get_low_quality_items():
    """获取低画质项目列表（分页）"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        offset = (page - 1) * per_page
        
        conn = get_db_connection()
        try:
            # 查询低画质项目
            query = """
                SELECT m.id, m.item_path, m.bangou, m.title, p.poster_path, p.poster_status, p.fanart_status 
                FROM movies m 
                JOIN pictures p ON m.id = p.movie_id 
                WHERE p.poster_status = '低画质' OR p.fanart_status = '低画质' 
                ORDER BY m.created_at DESC, m.id DESC 
                LIMIT ? OFFSET ?
            """
            items = conn.execute(query, (per_page, offset)).fetchall()
            
            # 查询总数
            total_query = """
                SELECT COUNT(m.id) 
                FROM movies m 
                JOIN pictures p ON m.id = p.movie_id 
                WHERE p.poster_status = '低画质' OR p.fanart_status = '低画质'
            """
            total = conn.execute(total_query).fetchone()[0]
            
            return jsonify({
                "success": True,
                "items": [dict(item) for item in items], 
                "total": total, 
                "page": page, 
                "per_page": per_page, 
                "has_more": total > page * per_page
            })
        finally:
            conn.close()
    except Exception as e:
        current_app.logger.error(f"获取低画质项目失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取低画质项目失败: {str(e)}"}), 500



@content_api.route('/refresh-item-images/<int:item_id>', methods=['POST'])
def refresh_item_images(item_id):
    """刷新项目图片信息"""
    try:
        import image_processor
        import os
        
        conn = get_db_connection()
        try:
            # 获取项目信息
            movie = conn.execute('SELECT item_path FROM movies WHERE id = ?', (item_id,)).fetchone()
            if not movie:
                return jsonify({"success": False, "message": "项目不存在"}), 404
            
            base_path = os.path.splitext(movie['item_path'])[0]
            
            # 获取图片详情
            p_w, p_h, p_s_kb, p_stat = image_processor.get_image_details(f"{base_path}-poster.jpg")
            f_w, f_h, f_s_kb, f_stat = image_processor.get_image_details(f"{base_path}-fanart.jpg")
            t_w, t_h, t_s_kb, t_stat = image_processor.get_image_details(f"{base_path}-thumb.jpg")
            
            # 更新数据库
            conn.execute("""
                UPDATE pictures SET 
                    poster_width = ?, poster_height = ?, poster_size_kb = ?, poster_status = ?,
                    fanart_width = ?, fanart_height = ?, fanart_size_kb = ?, fanart_status = ?,
                    thumb_width = ?, thumb_height = ?, thumb_size_kb = ?, thumb_status = ?
                WHERE movie_id = ?
            """, (p_w, p_h, p_s_kb, p_stat, f_w, f_h, f_s_kb, f_stat, t_w, t_h, t_s_kb, t_stat, item_id))
            conn.commit()
            
            # 返回更新后的数据
            updated_data = {
                "poster_status": p_stat, "fanart_status": f_stat, "thumb_status": t_stat,
                "poster_width": p_w, "poster_height": p_h, "poster_size_kb": p_s_kb,
                "fanart_width": f_w, "fanart_height": f_h, "fanart_size_kb": f_s_kb,
                "thumb_width": t_w, "thumb_height": t_h, "thumb_size_kb": t_s_kb
            }
            
            return jsonify({"success": True, "message": "图片信息已刷新", "data": updated_data})
        finally:
            conn.close()
    except Exception as e:
        current_app.logger.error(f"刷新项目图片失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"刷新项目图片失败: {str(e)}"}), 500
