# api_handler.py 重构规划详细方案

## 📊 当前代码分析

### 功能模块分析

#### 1. 基础设置管理模块 (约200行)
**路由列表**:
- `GET /settings` - 获取设置
- `POST /settings` - 保存设置  
- `POST /restart` - 重启应用
- `POST /test-notification` - 测试通知

**核心函数**:
- `get_settings_route()` (10行)
- `save_settings_route()` (30行)
- `restart_app()` (15行)
- `test_notification()` (20行)

**拆分难度**: ⭐⭐ (简单)
**依赖关系**: 仅依赖config_utils，无复杂内部调用

#### 2. 文件操作管理模块 (约300行)
**路由列表**:
- `GET /files/list` - 文件列表
- `POST /files/delete` - 删除文件
- `POST /files/rename` - 重命名文件
- `POST /files/move` - 移动文件

**核心函数**:
- `list_files()` (80行)
- `delete_file()` (40行)
- `rename_file()` (35行)
- `move_file()` (30行)

**拆分难度**: ⭐⭐ (简单)
**依赖关系**: 依赖utils中的安全检查函数

#### 3. 媒体服务模块 (约150行)
**路由列表**:
- `GET /media/<path:filename>` - 媒体文件服务
- `GET /watermarks/<path:filename>` - 水印文件服务

**核心函数**:
- `serve_media_file()` (60行)
- `serve_watermark_file()` (10行)
- `get_media_root()` (5行)

**拆分难度**: ⭐ (非常简单)
**依赖关系**: 最小依赖，主要是文件服务

#### 4. 图片处理模块 (约600行)
**路由列表**:
- `POST /process/poster` - 处理封面
- `POST /process/fanart-and-thumb` - 处理背景图和缩略图
- `POST /process/upload-image` - 上传图片处理
- `GET /cover-cache/list` - 封面缓存列表
- `POST /cover-cache/clear` - 清理封面缓存
- `POST /skip-item/<int:item_id>` - 跳过项目

**核心函数**:
- `process_poster_route()` (80行)
- `process_fanart_and_thumb_route()` (70行)
- `upload_and_process_image()` (120行)
- `get_cover_cache_list()` (60行)
- `clear_cover_cache()` (40行)
- `_update_db_pic_info()` (30行)

**拆分难度**: ⭐⭐⭐ (中等)
**依赖关系**: 依赖image_processor、数据库操作

#### 5. NFO管理模块 (约500行)
**路由列表**:
- `GET /handmade/nfo-details` - 手工NFO详情
- `POST /handmade/save-nfo` - 保存手工NFO
- `GET /manual/movie-details/<int:movie_id>` - 电影详情
- `GET /manual/nfo-content/<int:nfo_id>` - NFO内容
- `POST /manual/save-nfo/<int:nfo_id>` - 保存NFO
- `GET /manual/find-movie` - 查找电影

**核心函数**:
- `get_handmade_nfo_details()` (30行)
- `save_handmade_nfo()` (25行)
- `get_movie_details()` (15行)
- `get_nfo_content()` (20行)
- `save_nfo_content()` (80行)
- `find_movie()` (60行)

**拆分难度**: ⭐⭐⭐ (中等)
**依赖关系**: 依赖nfo_parser、数据库操作

#### 6. 元数据获取模块 (约400行)
**路由列表**:
- `GET /get-dmm-info` - 获取DMM信息
- `GET /get-manual-cid-info` - 手动获取CID信息
- `POST /verify-links` - 验证链接

**核心函数**:
- `get_dmm_info()` (25行)
- `get_manual_cid_info()` (35行)
- `verify_links()` (80行)
- `scrape_cid()` (150行)
- `check_dmm_domain_availability()` (5行)

**拆分难度**: ⭐⭐⭐⭐ (较难)
**依赖关系**: 复杂的网络请求和数据解析

#### 7. 性能监控模块 (约300行)
**路由列表**:
- `GET /api/performance/database/analyze` - 数据库分析
- `POST /api/performance/database/optimize` - 数据库优化
- `GET /api/performance/cache/stats` - 缓存统计
- `POST /api/performance/cache/clear` - 清理缓存
- `GET /api/performance/monitoring/dashboard` - 监控面板
- `POST /api/performance/test/comprehensive` - 综合测试
- `GET /api/performance/system/status` - 系统状态

**核心函数**:
- 各种性能监控相关的路由处理函数

**拆分难度**: ⭐⭐ (简单)
**依赖关系**: 依赖已有的性能监控模块

#### 8. 工具函数和全局变量 (约200行)
**内容**:
- HTTP Session创建和管理
- DMM域名缓存
- 封面缓存相关函数
- 安全路径检查
- 数据库更新辅助函数

**拆分难度**: ⭐⭐⭐ (中等)
**依赖关系**: 被多个模块依赖

## 🎯 拆分策略

### 拆分原则
1. **保持API接口不变** - 确保前端无需修改
2. **最小化依赖变更** - 减少模块间耦合
3. **渐进式重构** - 每次只拆分一个模块
4. **完整功能测试** - 每次拆分后完整测试

### 拆分顺序 (按难度和风险排序)

#### 第一阶段: 媒体服务模块 (风险最低)（已完成）
**目标**: 拆分最简单的文件服务功能
**文件**: `backend/api/media_api.py`
**预计时间**: 30分钟
**代码减少**: ~150行

#### 第二阶段: 基础设置管理模块（已完成）
**目标**: 拆分设置相关API
**文件**: `backend/api/settings_api.py`
**预计时间**: 45分钟
**代码减少**: ~200行

#### 第三阶段: 文件操作管理模块（已完成）
**目标**: 拆分文件操作相关API
**文件**: `backend/api/file_api.py`
**预计时间**: 1小时
**代码减少**: ~300行

#### 第四阶段: 性能监控模块（已完成）
**目标**: 整合现有性能监控API
**文件**: `backend/api/performance_api.py`
**预计时间**: 45分钟
**代码减少**: ~300行

#### 第五阶段: 图片处理模块
**目标**: 拆分图片处理相关API
**文件**: `backend/api/image_api.py`
**预计时间**: 2小时
**代码减少**: ~600行

#### 第六阶段: NFO管理模块
**目标**: 拆分NFO文件管理API
**文件**: `backend/api/nfo_api.py`
**预计时间**: 1.5小时
**代码减少**: ~500行

#### 第七阶段: 元数据获取模块
**目标**: 拆分元数据获取API
**文件**: `backend/api/metadata_api.py`
**预计时间**: 2小时
**代码减少**: ~400行

## 📁 目标架构

### 最终文件结构
```
backend/
├── api/
│   ├── __init__.py           # API模块初始化
│   ├── base.py              # 基础API类和装饰器
│   ├── media_api.py         # 媒体文件服务API
│   ├── settings_api.py      # 设置管理API
│   ├── file_api.py          # 文件操作API
│   ├── performance_api.py   # 性能监控API
│   ├── image_api.py         # 图片处理API
│   ├── nfo_api.py           # NFO管理API
│   └── metadata_api.py      # 元数据获取API
├── services/
│   ├── __init__.py
│   ├── image_service.py     # 图片处理服务
│   ├── nfo_service.py       # NFO处理服务
│   └── metadata_service.py  # 元数据服务
└── api_handler.py           # 主路由注册 (~100行)
```

### 重构后的api_handler.py结构
```python
# backend/api_handler.py (重构后约100行)
from flask import Blueprint
from api.media_api import media_api
from api.settings_api import settings_api
from api.file_api import file_api
from api.performance_api import performance_api
from api.image_api import image_api
from api.nfo_api import nfo_api
from api.metadata_api import metadata_api

api = Blueprint('api', __name__)

def init_app(app):
    """注册所有API蓝图"""
    app.register_blueprint(media_api, url_prefix='/api')
    app.register_blueprint(settings_api, url_prefix='/api')
    app.register_blueprint(file_api, url_prefix='/api')
    app.register_blueprint(performance_api, url_prefix='/api')
    app.register_blueprint(image_api, url_prefix='/api')
    app.register_blueprint(nfo_api, url_prefix='/api')
    app.register_blueprint(metadata_api, url_prefix='/api')
```