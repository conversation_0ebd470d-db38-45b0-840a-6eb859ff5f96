# backend/api/file_api.py
"""
文件操作管理API模块
负责处理文件和目录的操作，包括列表、删除、重命名、创建等功能
"""
import os
import logging
import signal
from flask import Blueprint, request, jsonify, current_app
from api.media_api import get_media_root, is_safe_path
from utils import safe_rename, safe_delete, safe_copy, get_safe_filename, ensure_dir_exists
from config_utils import get_settings
import time
import hashlib

logger = logging.getLogger(__name__)

# 创建文件操作API蓝图
file_api = Blueprint('file_api', __name__)

@file_api.route('/files/list', methods=['GET'])
def list_files():
    """
    获取文件列表API
    支持分页、筛选和简单模式
    """
    req_path = request.args.get('path', get_media_root())
    
    # 处理请求路径
    if not req_path:
        req_path = get_media_root()
    
    # 安全检查
    if not is_safe_path(req_path):
        current_app.logger.warning(f"拒绝访问路径: {req_path}, 媒体根路径: {get_media_root()}")
        return jsonify({"error": "禁止访问的路径", "details": f"请求路径: {req_path}, 媒体根路径: {get_media_root()}"}), 403
    
    # 确保路径存在
    if not os.path.exists(req_path):
        return jsonify({"error": "路径不存在", "path": req_path}), 404
    
    # 确保是目录
    if not os.path.isdir(req_path):
        return jsonify({"error": "不是有效目录", "path": req_path}), 400
    
    # 获取分页参数
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 200))
    simple_mode = request.args.get('simple', 'false').lower() == 'true'
    
    # 获取文件类型筛选器
    file_type_filters = request.args.getlist('file_types')
    
    try:
        # 设置超时，避免大目录处理时间过长
        def timeout_handler(signum, frame):
            _ = signum, frame  # 忽略未使用的参数
            raise TimeoutError("处理目录内容超时，目录可能包含太多文件")
        
        # 设置30秒超时
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)
        
        try:
            # 获取目录内所有项
            all_names = os.listdir(req_path)
            
            # 重置超时计时器
            signal.alarm(0)
        except TimeoutError as e:
            current_app.logger.warning(f"目录列表获取超时: {req_path}")
            return jsonify({"error": str(e)}), 504  # Gateway Timeout
        
        # 应用筛选器
        if file_type_filters:
            filtered_names = []
            for name in all_names:
                ext = os.path.splitext(name)[1].lower()
                # 如果没有扩展名但需要显示目录
                if (not ext and os.path.isdir(os.path.join(req_path, name)) and 'dir' in file_type_filters) or \
                   (ext and ext[1:] in file_type_filters):
                    filtered_names.append(name)
            all_names = filtered_names
        
        # 排序
        all_names.sort()
        
        # 分页处理
        total_items = len(all_names)
        total_pages = (total_items + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_items = all_names[start_idx:end_idx]
        
        items = []
        
        # 在简单模式下，只获取最基本的文件信息
        if simple_mode:
            for name in page_items:
                item_abs_path = os.path.join(req_path, name)
                try:
                    is_dir = os.path.isdir(item_abs_path)
                    items.append({
                        "name": name,
                        "path": item_abs_path,
                        "is_directory": is_dir,
                        "size": 0,  # 简单模式不获取大小
                        "modified_at": 0  # 简单模式不获取修改时间
                    })
                except (FileNotFoundError, PermissionError):
                    # 跳过无权限或丢失的文件
                    continue
        else:
            # 标准模式，获取更多文件详情
            for name in page_items:
                item_abs_path = os.path.join(req_path, name)
                try:
                    stat = os.stat(item_abs_path)
                    is_dir = os.path.isdir(item_abs_path)
                    
                    # 对于目录，只获取必要信息，不递归统计大小
                    items.append({
                        "name": name,
                        "path": item_abs_path,
                        "is_directory": is_dir,
                        "size": 0 if is_dir else stat.st_size,
                        "modified_at": stat.st_mtime
                    })
                except (FileNotFoundError, PermissionError):
                    # 跳过无权限或丢失的文件
                    continue
                
        return jsonify({
            "items": items,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_items": total_items,
                "total_pages": total_pages
            }
        })
    except FileNotFoundError:
        return jsonify({"error": "目录未找到"}), 404
    except PermissionError:
        return jsonify({"error": "没有权限访问该目录"}), 403
    except TimeoutError as e:
        return jsonify({"error": str(e)}), 504  # Gateway Timeout
    except Exception as e:
        current_app.logger.error(f"获取文件列表失败: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
    finally:
        # 确保超时信号被重置
        if 'signal' in locals():
            try:
                signal.alarm(0)
            except:
                pass

@file_api.route('/files/rename', methods=['POST'])
def rename_file():
    """
    重命名文件或目录API
    """
    old_path = request.json.get('path')
    new_name = request.json.get('new_name')
    
    if not all([old_path, new_name]):
        return jsonify({"error": "缺少必要参数"}), 400
    
    if not is_safe_path(old_path):
        return jsonify({"error": "无效的请求路径"}), 400
    
    new_path = os.path.join(os.path.dirname(old_path), new_name)
    if not is_safe_path(new_path):
        return jsonify({"error": "无效的新路径"}), 400
    
    try:
        success, error = safe_rename(old_path, new_path)
        if success:
            return jsonify({"success": True, "message": "重命名成功"})
        else:
            return jsonify({"error": error}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@file_api.route('/files/delete', methods=['POST'])
def delete_files():
    """
    删除文件或目录API
    支持批量删除
    """
    paths = request.json.get('paths', [])
    if not paths:
        return jsonify({"error": "没有提供要删除的路径"}), 400
    
    for path in paths:
        if not is_safe_path(path):
            return jsonify({"error": f"禁止删除路径: {path}"}), 403
        
        try:
            success, error = safe_delete(path)
            if not success:
                return jsonify({"error": error}), 500
        except Exception as e:
            return jsonify({"error": f"删除 {path} 失败: {e}"}), 500
    
    return jsonify({"success": True, "message": "删除成功"})

@file_api.route('/files/create-dir', methods=['POST'])
def create_directory():
    """
    创建目录API
    """
    parent_path = request.json.get('path')
    name = request.json.get('name')

    if not all([parent_path, name]):
        return jsonify({"error": "缺少必要参数"}), 400

    if not is_safe_path(parent_path):
        return jsonify({"error": "无效的父目录路径"}), 400

    new_dir_path = os.path.join(parent_path, name)
    if not is_safe_path(new_dir_path):
        return jsonify({"error": "无效的新目录路径"}), 400

    try:
        os.makedirs(new_dir_path, exist_ok=True)
        return jsonify({"success": True, "message": "目录创建成功"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@file_api.route('/files/move', methods=['POST'])
def move_file():
    """
    移动文件或目录API
    支持移动到不同目录
    """
    source_path = request.json.get('source_path')
    dest_path = request.json.get('dest_path')

    if not all([source_path, dest_path]):
        return jsonify({"error": "缺少必要参数"}), 400

    if not is_safe_path(source_path):
        return jsonify({"error": "无效的源路径"}), 400

    if not is_safe_path(dest_path):
        return jsonify({"error": "无效的目标路径"}), 400

    if not os.path.exists(source_path):
        return jsonify({"error": "源文件或目录不存在"}), 404

    try:
        # 如果目标路径是目录，则将源文件移动到该目录下
        if os.path.isdir(dest_path):
            dest_path = os.path.join(dest_path, os.path.basename(source_path))

        # 确保目标路径仍然安全
        if not is_safe_path(dest_path):
            return jsonify({"error": "计算后的目标路径无效"}), 400

        # 使用重命名实现移动（在同一文件系统内）
        success, error = safe_rename(source_path, dest_path)
        if success:
            return jsonify({"success": True, "message": "移动成功"})
        else:
            # 如果重命名失败，尝试复制+删除
            success_copy, error_copy = safe_copy(source_path, dest_path)
            if success_copy:
                success_del, error_del = safe_delete(source_path)
                if success_del:
                    return jsonify({"success": True, "message": "移动成功"})
                else:
                    return jsonify({"error": f"复制成功但删除源文件失败: {error_del}"}), 500
            else:
                return jsonify({"error": f"移动失败: {error_copy}"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# ==================== 封面缓存管理功能 ====================

def get_cover_cache_dir():
    """获取封面缓存目录路径"""
    settings = get_settings()
    # 默认在cover_cache目录下（与logs、db等目录同级）
    cache_dir = settings.get('cover_cache_dir', 'cover_cache')
    # 确保目录存在
    ensure_dir_exists(cache_dir)
    return cache_dir

def copy_to_cover_cache(poster_path, strm_name):
    """将封面图片复制到缓存目录"""
    if not poster_path or not strm_name:
        current_app.logger.warning(f"缓存封面失败: 无效的参数，poster_path={poster_path}, strm_name={strm_name}")
        return None

    # 检查源文件是否存在
    if not os.path.exists(poster_path):
        current_app.logger.warning(f"缓存封面失败: 源文件不存在 - {poster_path}")
        return None

    if not os.path.isfile(poster_path):
        current_app.logger.warning(f"缓存封面失败: 源路径不是文件 - {poster_path}")
        return None

    try:
        # 确保目标目录存在
        cache_dir = get_cover_cache_dir()

        # 使用strm_name的完整路径生成一个唯一的文件名，确保不同路径的同名影片不会冲突
        # 使用路径信息计算哈希，作为文件名前缀，保留原始番号作为文件名主体部分
        # 计算strm_name (通常是路径) 的哈希值的前8位作为前缀
        name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
        # 从strm_name中提取番号部分作为文件名主体
        base_name = os.path.basename(strm_name)
        if '.' in base_name:
            base_name = base_name.split('.')[0]  # 移除文件扩展名

        # 结合哈希和番号创建安全的文件名
        safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
        current_app.logger.debug(f"生成缓存文件名: {safe_name} (来源: {strm_name})")

        dest_path = os.path.join(cache_dir, f"{safe_name}.jpg")

        # 如果目标文件已存在且最近更新过（1小时内），跳过复制
        if os.path.exists(dest_path):
            source_mtime = os.path.getmtime(poster_path)
            dest_mtime = os.path.getmtime(dest_path)

            # 如果目标文件比源文件新，或者不超过1小时，则不更新
            one_hour = 60 * 60  # 秒数
            if dest_mtime >= source_mtime or (time.time() - dest_mtime) < one_hour:
                current_app.logger.debug(f"缓存封面已存在且较新: {safe_name}")
                return dest_path

        # 使用安全复制函数
        success, error = safe_copy(poster_path, dest_path)
        if success:
            current_app.logger.info(f"已缓存封面: {safe_name}")
            return dest_path
        else:
            current_app.logger.error(f"缓存封面失败: {error}")
            return None
    except Exception as e:
        current_app.logger.error(f"缓存封面失败: {str(e)}")
        return None

def get_cached_cover_path(strm_name):
    """获取缓存的封面路径，如果存在"""
    if not strm_name:
        return None

    try:
        cache_dir = get_cover_cache_dir()

        # 使用与copy_to_cover_cache相同的逻辑生成文件名
        name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
        base_name = os.path.basename(strm_name)
        if '.' in base_name:
            base_name = base_name.split('.')[0]

        safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
        cached_path = os.path.join(cache_dir, f"{safe_name}.jpg")

        if os.path.exists(cached_path):
            return cached_path

        # 向后兼容：尝试旧的命名方式
        old_safe_name = get_safe_filename(strm_name)
        old_cached_path = os.path.join(cache_dir, f"{old_safe_name}.jpg")
        if os.path.exists(old_cached_path):
            current_app.logger.debug(f"找到旧格式的缓存文件: {old_safe_name}")
            return old_cached_path

        return None
    except Exception as e:
        current_app.logger.error(f"获取缓存封面路径失败: {str(e)}")
        return None

def clean_cover_cache(max_covers=24):
    """清理封面缓存，只保留最新的指定数量的封面"""
    try:
        cache_dir = get_cover_cache_dir()
        if not os.path.exists(cache_dir):
            current_app.logger.info("封面缓存目录不存在，无需清理")
            return

        # 获取最新的项目列表
        from db_manager import get_db_connection
        conn = get_db_connection()
        try:
            # 获取最新的高画质项目 - 需要连接nfo_data和pictures表
            # 使用与api_handler.py中_get_latest_high_quality_items相同的排序逻辑
            items = conn.execute('''
                SELECT DISTINCT n.strm_name
                FROM nfo_data n
                JOIN pictures p ON n.movie_id = p.movie_id
                JOIN movies m ON n.movie_id = m.id
                WHERE p.poster_status = '高画质'
                AND p.poster_path IS NOT NULL
                AND p.poster_path != ''
                AND n.strm_name IS NOT NULL
                ORDER BY m.created_at DESC, m.id DESC
                LIMIT ?
            ''', (max_covers,)).fetchall()
        finally:
            conn.close()

        # 获取最新的项目列表
        latest_items = [dict(item) for item in items]

        # 为每个项目生成可能的缓存文件名列表（包括新旧命名方式）
        to_keep_filenames = set()

        for item in latest_items:
            strm_name = item.get('strm_name')
            if not strm_name:
                continue

            # 新命名方式
            name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
            base_name = os.path.basename(strm_name)
            if '.' in base_name:
                base_name = base_name.split('.')[0]

            safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
            new_filename = f"{safe_name}.jpg"
            to_keep_filenames.add(new_filename)

            # 旧命名方式
            old_safe_name = get_safe_filename(strm_name)
            old_filename = f"{old_safe_name}.jpg"
            to_keep_filenames.add(old_filename)

        # 删除不在保留列表中的文件
        deleted_count = 0
        for filename in os.listdir(cache_dir):
            if filename.endswith('.jpg') and filename not in to_keep_filenames:
                try:
                    file_path = os.path.join(cache_dir, filename)
                    os.remove(file_path)
                    deleted_count += 1
                    current_app.logger.debug(f"删除多余的缓存文件: {filename}")
                except Exception as e:
                    current_app.logger.error(f"删除缓存文件失败: {filename}, 错误: {str(e)}")

        if deleted_count > 0:
            current_app.logger.info(f"封面缓存清理完成: 删除了{deleted_count}个多余文件，保留最新的{max_covers}个")
        else:
            current_app.logger.debug("封面缓存无需清理")

    except Exception as e:
        current_app.logger.error(f"清理封面缓存失败: {str(e)}", exc_info=True)

def manage_cover_cache():
    """管理封面缓存，自动清理多余的缓存文件"""
    try:
        settings = get_settings()
        max_covers = settings.get('latest_movies_count', 24)
        clean_cover_cache(max_covers)
    except Exception as e:
        current_app.logger.error(f"管理封面缓存失败: {str(e)}", exc_info=True)

def _get_latest_high_quality_items(count):
    """获取最新的高画质项目（内部函数）"""
    from db_manager import get_db_connection
    conn = get_db_connection()
    try:
        items = conn.execute('''
            SELECT DISTINCT n.strm_name, p.poster_path
            FROM nfo_data n
            JOIN pictures p ON n.movie_id = p.movie_id
            JOIN movies m ON n.movie_id = m.id
            WHERE p.poster_status = '高画质'
            AND p.poster_path IS NOT NULL
            AND p.poster_path != ''
            AND n.strm_name IS NOT NULL
            ORDER BY m.created_at DESC, m.id DESC
            LIMIT ?
        ''', (count,)).fetchall()
        return [dict(item) for item in items]
    finally:
        conn.close()

# ==================== 封面缓存API路由 ====================

@file_api.route('/cover-cache', methods=['GET'])
def get_cover_cache_status():
    """获取封面缓存状态"""
    try:
        cache_dir = get_cover_cache_dir()
        if not os.path.exists(cache_dir):
            return jsonify({"success": False, "message": "缓存目录不存在"}), 404

        # 获取所有缓存的封面文件
        covers = []
        total_size = 0
        for filename in os.listdir(cache_dir):
            if filename.endswith('.jpg'):
                file_path = os.path.join(cache_dir, filename)
                file_size = os.path.getsize(file_path) / 1024  # 转换为KB
                total_size += file_size
                covers.append({
                    "filename": filename,
                    "path": file_path,
                    "size_kb": round(file_size, 2),
                    "modified_at": os.path.getmtime(file_path)
                })

        # 按修改时间排序
        covers.sort(key=lambda x: x['modified_at'], reverse=True)

        settings = get_settings()
        max_covers = settings.get('latest_movies_count', 24)

        return jsonify({
            "success": True,
            "cache_dir": cache_dir,
            "total_files": len(covers),
            "total_size_kb": round(total_size, 2),
            "max_covers": max_covers,
            "covers": covers
        })
    except Exception as e:
        current_app.logger.error(f"获取封面缓存状态失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取封面缓存状态失败: {str(e)}"}), 500

@file_api.route('/cover-cache/refresh', methods=['POST'])
def refresh_cover_cache():
    """刷新封面缓存"""
    try:
        settings = get_settings()
        count = settings.get('latest_movies_count', 24)

        # 获取最新的高画质项目
        items = _get_latest_high_quality_items(count)

        # 清理现有缓存
        cache_dir = get_cover_cache_dir()
        deleted_count = 0
        if os.path.isdir(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.jpg'):
                    try:
                        os.remove(os.path.join(cache_dir, filename))
                        deleted_count += 1
                    except Exception as e:
                        current_app.logger.error(f"删除缓存文件失败: {filename}, 错误: {str(e)}")

        if deleted_count > 0:
            current_app.logger.info(f"已清理旧缓存: 删除了{deleted_count}个文件")

        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)

        # 创建新的缓存
        cache_count = 0
        for item in items:
            strm_name = item['strm_name']
            poster_path = item['poster_path']
            if strm_name and poster_path:
                if copy_to_cover_cache(poster_path, strm_name):
                    cache_count += 1

        return jsonify({
            "success": True,
            "message": f"封面缓存刷新成功，已缓存 {cache_count} 个封面",
            "cache_count": cache_count
        })
    except Exception as e:
        current_app.logger.error(f"刷新封面缓存失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"刷新封面缓存失败: {str(e)}"}), 500

@file_api.route('/cover-cache/clean', methods=['POST'])
def clean_cover_cache_route():
    """清理封面缓存"""
    try:
        settings = get_settings()
        max_covers = settings.get('latest_movies_count', 24)
        clean_cover_cache(max_covers)
        return jsonify({"success": True, "message": f"已清理多余的封面缓存，保留最新的 {max_covers} 个"})
    except Exception as e:
        current_app.logger.error(f"清理封面缓存失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"清理封面缓存失败: {str(e)}"}), 500
