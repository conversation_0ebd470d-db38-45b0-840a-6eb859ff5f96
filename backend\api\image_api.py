# backend/api/image_api.py
"""
图片处理API模块
负责处理图片上传、处理、封面缓存等功能
"""
import os
import tempfile
import uuid
import hashlib

from flask import Blueprint, request, jsonify, current_app, send_from_directory
from werkzeug.utils import secure_filename

from db_manager import get_db_connection
import image_processor
from config_utils import get_settings
from api.media_api import is_safe_path
from utils import get_safe_filename, ensure_dir_exists

# 创建蓝图
image_api = Blueprint('image_api', __name__)

# ==================== 核心函数 ====================

def _update_db_pic_info(conn, movie_id, target_type, save_path):
    """更新数据库中的图片信息"""
    width, height, size_kb, status = image_processor.get_image_details(save_path)
    update_query = f"UPDATE pictures SET {target_type}_path = ?, {target_type}_width = ?, {target_type}_height = ?, {target_type}_size_kb = ?, {target_type}_status = ? WHERE movie_id = ?"
    conn.execute(update_query, (save_path, width, height, size_kb, status, movie_id))
    current_app.logger.info(f"DB Updated for {target_type}: {save_path}, Status: {status}")

# ==================== 封面缓存管理功能 ====================

def get_cover_cache_dir():
    """获取封面缓存目录路径"""
    settings = get_settings()
    # 默认在cover_cache目录下（与logs、db等目录同级）
    cache_dir = settings.get('cover_cache_dir', 'cover_cache')
    # 确保目录存在
    ensure_dir_exists(cache_dir)
    return cache_dir

def copy_to_cover_cache(poster_path, strm_name):
    """将封面图片复制到缓存目录"""
    if not poster_path or not strm_name:
        current_app.logger.warning(f"缓存封面失败: 无效的参数，poster_path={poster_path}, strm_name={strm_name}")
        return None

    # 检查源文件是否存在
    if not os.path.exists(poster_path):
        current_app.logger.warning(f"缓存封面失败: 源文件不存在 - {poster_path}")
        return None

    if not os.path.isfile(poster_path):
        current_app.logger.warning(f"缓存封面失败: 源路径不是文件 - {poster_path}")
        return None

    try:
        cache_dir = get_cover_cache_dir()
        
        # 生成缓存文件名 - 使用哈希值避免文件名过长或包含特殊字符
        name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
        base_name = os.path.basename(strm_name)
        if '.' in base_name:
            base_name = base_name.split('.')[0]
        
        safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
        cache_filename = f"{safe_name}.jpg"
        cache_path = os.path.join(cache_dir, cache_filename)
        
        # 如果缓存文件已存在且源文件没有更新，则跳过
        if os.path.exists(cache_path):
            cache_mtime = os.path.getmtime(cache_path)
            source_mtime = os.path.getmtime(poster_path)
            if cache_mtime >= source_mtime:
                current_app.logger.debug(f"封面缓存已存在且是最新的: {cache_path}")
                return cache_path
        
        # 复制文件到缓存目录
        import shutil
        shutil.copy2(poster_path, cache_path)
        current_app.logger.info(f"封面已缓存: {poster_path} -> {cache_path}")
        return cache_path
        
    except Exception as e:
        current_app.logger.error(f"缓存封面失败: {poster_path}, 错误: {str(e)}", exc_info=True)
        return None

def get_cached_cover_path(strm_name):
    """获取缓存的封面路径"""
    if not strm_name:
        return None
        
    try:
        cache_dir = get_cover_cache_dir()
        
        # 生成可能的缓存文件名（新命名方式）
        name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
        base_name = os.path.basename(strm_name)
        if '.' in base_name:
            base_name = base_name.split('.')[0]
        
        safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
        new_cache_filename = f"{safe_name}.jpg"
        new_cache_path = os.path.join(cache_dir, new_cache_filename)
        
        if os.path.exists(new_cache_path):
            return new_cache_path
        
        # 尝试旧命名方式
        old_safe_name = get_safe_filename(strm_name)
        old_cache_filename = f"{old_safe_name}.jpg"
        old_cache_path = os.path.join(cache_dir, old_cache_filename)
        
        if os.path.exists(old_cache_path):
            return old_cache_path
            
        return None
        
    except Exception as e:
        current_app.logger.error(f"获取缓存封面路径失败: {strm_name}, 错误: {str(e)}", exc_info=True)
        return None

def clean_cover_cache(max_covers=24):
    """清理封面缓存，只保留最新的指定数量的封面"""
    try:
        cache_dir = get_cover_cache_dir()
        if not os.path.exists(cache_dir):
            current_app.logger.info("封面缓存目录不存在，无需清理")
            return

        # 获取最新的项目列表
        conn = get_db_connection()
        try:
            # 获取最新的高画质项目 - 需要连接nfo_data和pictures表
            items = conn.execute('''
                SELECT DISTINCT n.strm_name
                FROM nfo_data n
                JOIN pictures p ON n.movie_id = p.movie_id
                JOIN movies m ON n.movie_id = m.id
                WHERE p.poster_status = '高画质'
                AND p.poster_path IS NOT NULL
                AND p.poster_path != ''
                AND n.strm_name IS NOT NULL
                ORDER BY m.created_at DESC, m.id DESC
                LIMIT ?
            ''', (max_covers,)).fetchall()
        finally:
            conn.close()

        # 获取最新的项目列表
        latest_items = [dict(item) for item in items]

        # 为每个项目生成可能的缓存文件名列表（包括新旧命名方式）
        to_keep_filenames = set()

        for item in latest_items:
            strm_name = item.get('strm_name')
            if not strm_name:
                continue

            # 新命名方式
            name_hash = hashlib.md5(strm_name.encode('utf-8')).hexdigest()[:8]
            base_name = os.path.basename(strm_name)
            if '.' in base_name:
                base_name = base_name.split('.')[0]

            safe_name = f"{name_hash}_{get_safe_filename(base_name)}"
            new_filename = f"{safe_name}.jpg"
            to_keep_filenames.add(new_filename)

            # 旧命名方式
            old_safe_name = get_safe_filename(strm_name)
            old_filename = f"{old_safe_name}.jpg"
            to_keep_filenames.add(old_filename)

        # 删除不在保留列表中的文件
        deleted_count = 0
        if os.path.isdir(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.jpg') and filename not in to_keep_filenames:
                    try:
                        file_path = os.path.join(cache_dir, filename)
                        os.remove(file_path)
                        deleted_count += 1
                        current_app.logger.debug(f"删除多余的缓存文件: {filename}")
                    except Exception as e:
                        current_app.logger.error(f"删除缓存文件失败: {filename}, 错误: {str(e)}")

        if deleted_count > 0:
            current_app.logger.info(f"封面缓存清理完成: 删除了{deleted_count}个多余文件，保留了{len(to_keep_filenames)}个最新文件")
        else:
            current_app.logger.info("封面缓存无需清理")

    except Exception as e:
        current_app.logger.error(f"清理封面缓存失败: {str(e)}", exc_info=True)
        raise

def _get_latest_high_quality_items(count):
    """获取最新的高画质项目"""
    conn = get_db_connection()
    try:
        items = conn.execute('''
            SELECT DISTINCT n.strm_name, p.poster_path
            FROM nfo_data n
            JOIN pictures p ON n.movie_id = p.movie_id
            JOIN movies m ON n.movie_id = m.id
            WHERE p.poster_status = '高画质'
            AND p.poster_path IS NOT NULL
            AND p.poster_path != ''
            AND n.strm_name IS NOT NULL
            ORDER BY m.created_at DESC, m.id DESC
            LIMIT ?
        ''', (count,)).fetchall()
        return [dict(item) for item in items]
    finally:
        conn.close()

# ==================== 图片处理路由 ====================

@image_api.route('/process/poster', methods=['POST'])
def process_poster_route():
    """处理封面图片"""
    data = request.json
    movie_id, image_url, watermarks, crop = data.get('item_id'), data.get('image_url'), data.get('watermarks', []), data.get('crop', False)
    if not image_url:
        return jsonify({"success": False, "message": "缺少参数"}), 400

    settings = get_settings()

    # 处理保存路径 - 如果提供了base_path，则使用它，否则尝试从movie_id获取
    save_path = None
    if data.get('base_path'):
        save_path = f"{data.get('base_path')}-poster.jpg"
    elif movie_id:
        conn = get_db_connection()
        movie = conn.execute('SELECT item_path FROM movies WHERE id = ?', (movie_id,)).fetchone()
        if not movie:
            conn.close()
            return jsonify({"success": False, "message": "项目不存在"}), 404
        save_path = f"{os.path.splitext(movie['item_path'])[0]}-poster.jpg"
        conn.close()
    else:
        return jsonify({"success": False, "message": "缺少保存路径信息"}), 400

    success, msg = image_processor.process_image_from_url(image_url, save_path, 'poster', settings, watermarks, crop_for_poster=crop)

    # 如果成功且有movie_id，更新数据库
    if success and movie_id:
        conn = get_db_connection()
        _update_db_pic_info(conn, movie_id, 'poster', save_path)
        conn.commit()
        conn.close()

    return jsonify({"success": success, "message": msg})

@image_api.route('/process/fanart-and-thumb', methods=['POST'])
def process_fanart_and_thumb_route():
    """处理背景图和缩略图"""
    data = request.json
    movie_id, image_url, watermarks, crop_poster_flag = data.get('item_id'), data.get('image_url'), data.get('watermarks', []), data.get('crop_poster', False)
    if not image_url:
        return jsonify({"success": False, "message": "缺少参数"}), 400

    settings = get_settings()

    # 处理保存路径 - 如果提供了base_path，则使用它，否则尝试从movie_id获取
    base_path = data.get('base_path')
    if not base_path and movie_id:
        conn = get_db_connection()
        movie = conn.execute('SELECT item_path FROM movies WHERE id = ?', (movie_id,)).fetchone()
        if not movie:
            conn.close()
            return jsonify({"success": False, "message": "项目不存在"}), 404
        base_path = os.path.splitext(movie['item_path'])[0]
        conn.close()

    if not base_path:
        return jsonify({"success": False, "message": "缺少保存路径信息"}), 400

    fanart_path = f"{base_path}-fanart.jpg"
    fanart_success, _ = image_processor.process_image_from_url(image_url, fanart_path, 'fanart', settings, watermarks, crop_for_poster=False)

    thumb_path = f"{base_path}-thumb.jpg"
    thumb_success, _ = image_processor.process_image_from_url(image_url, thumb_path, 'thumb', settings, watermarks, crop_for_poster=False)

    if crop_poster_flag:
        poster_path = f"{base_path}-poster.jpg"
        poster_success, _ = image_processor.process_image_from_url(image_url, poster_path, 'poster', settings, watermarks, crop_for_poster=True)

    # 如果有movie_id，更新数据库
    if movie_id:
        conn = get_db_connection()
        if fanart_success:
            _update_db_pic_info(conn, movie_id, 'fanart', fanart_path)
        if thumb_success:
            _update_db_pic_info(conn, movie_id, 'thumb', thumb_path)
        if crop_poster_flag and poster_success:
            _update_db_pic_info(conn, movie_id, 'poster', poster_path)
        conn.commit()
        conn.close()

    return jsonify({"success": True, "message": "图片处理完成"})

@image_api.route('/process/upload-image', methods=['POST'])
def upload_and_process_image():
    """
    处理上传的图片，添加水印并返回处理后的图片
    可以选择直接保存到特定路径
    """
    if 'image' not in request.files:
        return jsonify({"success": False, "message": "没有上传图片"}), 400

    image_file = request.files['image']
    if image_file.filename == '':
        return jsonify({"success": False, "message": "未选择图片"}), 400

    # 处理参数
    watermarks = request.form.getlist('watermarks[]') if 'watermarks[]' in request.form else []
    target_type = request.form.get('target_type', 'preview')  # 'preview', 'poster', 'fanart', 'thumb'
    crop_for_poster = request.form.get('crop_for_poster', 'false').lower() == 'true'
    save_path = request.form.get('save_path', '')

    # 保存上传的图片到临时文件
    temp_dir = tempfile.mkdtemp()
    try:
        temp_path = os.path.join(temp_dir, secure_filename(image_file.filename))
        image_file.save(temp_path)

        settings = get_settings()

        if target_type == 'preview':
            # 处理预览模式 - 返回处理后的图片，但不保存
            output_temp = os.path.join(temp_dir, f"preview_{uuid.uuid4().hex}.jpg")
            with open(temp_path, 'rb') as f:
                img = image_processor.Image.open(f).convert("RGB")

                if crop_for_poster:
                    crop_ratio = float(settings.get('poster_crop_ratio', 1.419))
                    target_ratio = 1 / crop_ratio
                    current_ratio = img.width / img.height
                    if current_ratio > target_ratio:
                        new_width = int(target_ratio * img.height)
                        left = img.width - new_width
                        img = img.crop((left, 0, img.width, img.height))

                # 只有在预览模式下，我们总是应用水印
                img = image_processor.add_watermarks(img, watermarks, settings)
                img.save(output_temp, "JPEG", quality=95)

            # 设置响应类型为图片
            return send_from_directory(os.path.dirname(output_temp),
                                      os.path.basename(output_temp),
                                      as_attachment=True,
                                      mimetype='image/jpeg')
        else:
            # 保存模式 - 处理并保存到指定路径
            if not save_path:
                return jsonify({"success": False, "message": "未指定保存路径"}), 400

            if not is_safe_path(save_path):
                return jsonify({"success": False, "message": "无效的保存路径"}), 403

            success, msg = image_processor.process_image_from_url(
                f"file://{temp_path}", save_path, target_type, settings, watermarks, crop_for_poster
            )

            # 如果是针对特定movie_id的，更新数据库
            movie_id = request.form.get('movie_id')
            if success and movie_id and movie_id.isdigit():
                conn = get_db_connection()
                try:
                    _update_db_pic_info(conn, int(movie_id), target_type, save_path)
                    conn.commit()
                finally:
                    conn.close()

            return jsonify({"success": success, "message": msg})

    except Exception as e:
        current_app.logger.error(f"处理上传图片失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"处理图片失败: {str(e)}"}), 500
    finally:
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except Exception as e:
            current_app.logger.warning(f"清理临时文件失败: {str(e)}")

@image_api.route('/skip-item/<int:item_id>', methods=['POST'])
def skip_item(item_id):
    """跳过项目（标记为高画质）"""
    try:
        conn = get_db_connection()
        try:
            # 更新项目状态为高画质
            conn.execute(
                "UPDATE pictures SET poster_status = '高画质', fanart_status = '高画质' WHERE movie_id = ?",
                (item_id,)
            )
            conn.commit()

            return jsonify({"success": True, "message": "项目已跳过"})
        finally:
            conn.close()
    except Exception as e:
        current_app.logger.error(f"跳过项目失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"跳过项目失败: {str(e)}"}), 500

# ==================== 封面缓存API路由 ====================

@image_api.route('/cover-cache/list', methods=['GET'])
def get_cover_cache_list():
    """获取封面缓存列表"""
    try:
        cache_dir = get_cover_cache_dir()
        if not os.path.exists(cache_dir):
            return jsonify({"success": False, "message": "缓存目录不存在"}), 404

        # 获取所有缓存的封面文件
        covers = []
        total_size = 0
        for filename in os.listdir(cache_dir):
            if filename.endswith('.jpg'):
                file_path = os.path.join(cache_dir, filename)
                file_size = os.path.getsize(file_path) / 1024  # 转换为KB
                total_size += file_size
                covers.append({
                    "filename": filename,
                    "path": file_path,
                    "size_kb": round(file_size, 2),
                    "modified_at": os.path.getmtime(file_path)
                })

        # 按修改时间排序
        covers.sort(key=lambda x: x['modified_at'], reverse=True)

        settings = get_settings()
        max_covers = settings.get('latest_movies_count', 24)

        return jsonify({
            "success": True,
            "cache_dir": cache_dir,
            "total_files": len(covers),
            "total_size_kb": round(total_size, 2),
            "max_covers": max_covers,
            "covers": covers
        })
    except Exception as e:
        current_app.logger.error(f"获取封面缓存列表失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取封面缓存列表失败: {str(e)}"}), 500

@image_api.route('/cover-cache/refresh', methods=['POST'])
def refresh_cover_cache():
    """刷新封面缓存"""
    try:
        settings = get_settings()
        count = settings.get('latest_movies_count', 24)

        # 获取最新的高画质项目
        items = _get_latest_high_quality_items(count)

        # 清理现有缓存
        cache_dir = get_cover_cache_dir()
        deleted_count = 0
        if os.path.isdir(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.jpg'):
                    try:
                        os.remove(os.path.join(cache_dir, filename))
                        deleted_count += 1
                    except Exception as e:
                        current_app.logger.error(f"删除缓存文件失败: {filename}, 错误: {str(e)}")

        if deleted_count > 0:
            current_app.logger.info(f"已清理旧缓存: 删除了{deleted_count}个文件")

        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)

        # 创建新的缓存
        cache_count = 0
        for item in items:
            strm_name = item['strm_name']
            poster_path = item['poster_path']
            if strm_name and poster_path:
                if copy_to_cover_cache(poster_path, strm_name):
                    cache_count += 1

        return jsonify({
            "success": True,
            "message": f"封面缓存刷新成功，已缓存 {cache_count} 个封面",
            "cache_count": cache_count
        })
    except Exception as e:
        current_app.logger.error(f"刷新封面缓存失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"刷新封面缓存失败: {str(e)}"}), 500

@image_api.route('/cover-cache/clear', methods=['POST'])
def clear_cover_cache():
    """清理封面缓存"""
    try:
        settings = get_settings()
        max_covers = settings.get('latest_movies_count', 24)
        clean_cover_cache(max_covers)
        return jsonify({"success": True, "message": f"已清理多余的封面缓存，保留最新的 {max_covers} 个"})
    except Exception as e:
        current_app.logger.error(f"清理封面缓存失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"清理封面缓存失败: {str(e)}"}), 500
